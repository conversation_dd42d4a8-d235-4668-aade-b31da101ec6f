using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Views.Players;
using Modules.Core;
using Modules.XR;
using VContainer;

namespace Game.Controllers.Players
{
    public class PlayerScaleController : ControllerBase
    {
        private IXRPlayer xrPlayer;
        private PlayersModel playersModel;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;
        private OfflinePlayerActor OfflinePlayer => playersModel.OfflineLocalPlayer.Value;

        [Inject]
        private void Construct(PlayersModel playersModel, IXRPlayer xrPlayer)
        {
            this.xrPlayer = xrPlayer;
            this.playersModel = playersModel;

            playersModel.OnLocalPlayerBeforeCreated.Subscribe(HandleLocalPlayerBeforeSpawned).AddTo(DisposeCancellationToken);
            playersModel.OfflineLocalPlayer.Where(p => p).Subscribe(HandleOfflinePlayer).AddTo(DisposeCancellationToken);
            playersModel.LocalPlayer.Where(p => p).Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayerBeforeSpawned(PlayerActor player)
        {
            player.SetPlayerScale(CoreConstants.WorldScale);
        }

        private void HandleOfflinePlayer(OfflinePlayerActor player)
        {
            if (LocalPlayer != null)
            {
                player.SetScale(LocalPlayer.Scale.Value);
                LocalPlayer.Scale.Subscribe(player.SetScale).AddTo(player);
            }
            else
            {
                player.SetScale(CoreConstants.WorldScale);
            }
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            player.Scale.Subscribe(HandleScale).AddTo(player);
        }

        private void HandleScale(float scale)
        {
            xrPlayer.SetScale(scale);

            if (OfflinePlayer != null)
            {
                OfflinePlayer.SetScale(scale);
            }
        }
    }
}