using System;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Views.Avatars;
using Game.Views.Badges;
using Game.Views.Interactables;
using Game.Views.Wings;
using MessagePipe;
using Modules.Core;
using Modules.Network;
using Modules.XR;
using Photon.Voice.Fusion;
using UnityEngine;
using VContainer;

namespace Game.Views.Players
{
    public partial class PlayerActor : NetworkActor
    {
        [SerializeField] private NetworkRig networkRig;
        [SerializeField] private PlayerView playerView;
        [SerializeField] private AudioSource voiceSource;
        [SerializeField] private Rigidbody selfRigidbody;
        [SerializeField] private Transform leftPlaceholder;
        [SerializeField] private Transform rightPlaceholder;
        [SerializeField] private WingsProvider wingsProvider;
        [SerializeField] private AvatarProvider avatarProvider;
        [SerializeField] private CapsuleCollider capsuleCollider;
        [SerializeField] private VoiceNetworkObject voiceNetworkObject;
        [SerializeField] private LocalPokePointerProvider localPokePointerProvider;

        private IXRInput xrInput;
        private IXRPlayer xrPlayer;
        private BadgeView badgeView;
        private IAudioClient audioClient;
        private BadgesConfig badgesConfig;
        private BadgesManager badgesManager;
        private AvatarsConfig avatarsConfig;
        private PlayersConfig playersConfig;
        private IPokePointerProvider pokePointerProvider;
        private IFingerPosesProvider fingerPosesProvider;
        private InteractablesManager interactablesManager;
        private IPublisher<AvatarChangeArgs> avatarChangePublisher;
        private IPublisher<WingsUpdateArgs> detachableWingsPublisher;

        public Transform HeadNode => networkRig.HeadNode;
        public NetworkHand LeftNetworkHand => networkRig.LeftNetworkHand;
        public NetworkHand RightNetworkHand => networkRig.RightNetworkHand;

        [Inject]
        private void Construct(
            IXRInput xrInput,
            IXRPlayer xrPlayer,
            IAudioClient audioClient,
            BadgesConfig badgesConfig,
            BadgesManager badgesManager,
            AvatarsConfig avatarsConfig,
            PlayersConfig playersConfig,
            InteractablesManager interactablesManager,
            IPublisher<WingsUpdateArgs> detachableWingsPublisher,
            IPublisher<AvatarChangeArgs> avatarChangePublisher)
        {
            this.badgesConfig = badgesConfig;
            this.xrInput = xrInput;
            this.xrPlayer = xrPlayer;
            this.audioClient = audioClient;
            this.avatarsConfig = avatarsConfig;
            this.playersConfig = playersConfig;
            this.badgesManager = badgesManager;
            this.interactablesManager = interactablesManager;
            this.avatarChangePublisher = avatarChangePublisher;
            this.detachableWingsPublisher = detachableWingsPublisher;
        }

        public override void Spawned()
        {
            base.Spawned();
#if UNITY_EDITOR
            name = $"Player_{PlayerId}";
#endif
            InitializePlayerLayer();
            InitializePlayerView();
            InitializeFingerPoses();
            InitializePokePointer();
            InitializeAvatar();
            InitializeRig();

            ChangeName();
            ChangeAvatarId();
            ChangeBadgeId();
            ChangeWingId();
            ChangeHatId();
            ChangeSuitId();
            ChangeIsTaggedNetworked();
            ChangeIsInitialVoxelsReceivedNetwork();
            ChangeIsVoiceDetected();
            ChangeIsMutedByAdmin();
            ChangeIsInPrison();
            ChangeDayCountNetworked();
            ChangeKilledZombieCountNetworked();
            ChangeHealthNetworked();
            ChangeBackpack();
            ChangeScaleNetworked();
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);
            pokePointerProvider.RemovePointers();
            avatarProvider.ClearAvatar();
            DestroyBadge();
            DestroyCrown();
        }

        public override void Render()
        {
            base.Render();
            RenderRemoteBackpackPose();
            UpdateVoiceDetection();
            fingerPosesProvider?.Update();
        }

        public bool TryGetAvatarHandPose(out AvatarHandPose leftHandPose, out AvatarHandPose rightHandPose)
        {
            return avatarProvider.TryGetHandPoses(out leftHandPose, out rightHandPose);
        }

        private void InitializePlayerLayer()
        {
            gameObject.layer = capsuleCollider.gameObject.layer = HasStateAuthority ? Layers.LocalPlayer : Layers.RemotePlayer;
        }

        private void InitializePlayerView()
        {
            playerView.SetActiveViewObject(HasStateAuthority);
        }

        private void InitializeRig()
        {
            if (HasStateAuthority)
            {
                xrPlayer.OnPoseUpdated.Subscribe(pose =>
                {
                    networkRig.SetPose(pose);
                    avatarProvider.SetWorldPose(pose);
                    RenderLocalBackpackPose();
                }).AddTo(destroyCancellationToken);
            }
            else
            {
                networkRig.OnPoseUpdated.Subscribe(avatarProvider.SetWorldPose).AddTo(destroyCancellationToken);
            }
        }

        private void InitializePokePointer()
        {
            pokePointerProvider = HasStateAuthority ? localPokePointerProvider : new RemotePokePointerProvider();
        }
    }
}