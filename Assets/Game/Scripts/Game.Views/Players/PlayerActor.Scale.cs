using Fusion;
using Game.Core;
using UnityEngine;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        private readonly IAsyncReactiveProperty<float> scale = new AsyncReactiveProperty<float>(1.0f);
        public IReadOnlyAsyncReactiveProperty<float> Scale => scale;

        [Networked] [OnChangedRender(nameof(ChangeScaleNetworked))]
        private float ScaleNetworked { get; set; } = 1.0f;

        public override void SetScale(float newScale)
        {
            if (HasStateAuthority && newScale != ScaleNetworked)
            {
                ScaleNetworked = newScale;
            }
        }

        public float GetCurrentScale()
        {
            return ScaleNetworked;
        }

        private void ChangeScaleNetworked()
        {
            scale.Value = ScaleNetworked;
            if (!HasStateAuthority)
            {
                transform.localScale = ScaleNetworked * Vector3.one;
            }
        }
    }
}
