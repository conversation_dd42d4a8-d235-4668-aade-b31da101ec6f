using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Modules.Core;
using UnityEngine;

namespace Game.Views.Players
{
    public partial class PlayersModel
    {
        private readonly List<int> ignoredPlayers = new();
        private readonly ISubject<PlayerRespawnArgs> onLocalPlayerRespawning = new Subject<PlayerRespawnArgs>();
        private readonly ISubject<bool> onLocomotionsActivationUpdating = new Subject<bool>();
        private readonly ISubject<PlayerTeleportArgs> onLocalPlayerTeleporting = new Subject<PlayerTeleportArgs>();
        private readonly IAsyncReactiveProperty<ActiveInteractableList> activeInteractableList;


        public IObservable<PlayerRespawnArgs> OnLocalPlayerRespawning => onLocalPlayerRespawning;
        public IObservable<bool> OnLocomotionsActivationUpdating => onLocomotionsActivationUpdating;
        public IObservable<PlayerTeleportArgs> OnLocalPlayerTeleporting => onLocalPlayerTeleporting;

        public IReadOnlyAsyncReactiveProperty<ActiveInteractableList> ActiveInteractableList => activeInteractableList;

        public string HatCode
        {
            get => localStorage.HatCode;
            set => localStorage.HatCode = value;
        }

        public string SuitCode
        {
            get => localStorage.SuitCode;
            set => localStorage.SuitCode = value;
        }

        public string AvatarCode
        {
            get => localStorage.AvatarCode;
            set => localStorage.AvatarCode = value;
        }

        public string BadgeCode
        {
            get => localStorage.BadgeCode;
            set => localStorage.BadgeCode = value;
        }

        public void AddActiveInteractable(HandType hand, int index, string code)
        {
            activeInteractableList.Value.Add(hand, index, code);
            activeInteractableList.Value = activeInteractableList.Value;
            activeInteractableList.Value.Save();
        }

        public void RemoveActiveInteractable(HandType hand, int index)
        {
            activeInteractableList.Value.Remove(hand, index);
            activeInteractableList.Value = activeInteractableList.Value;
            activeInteractableList.Value.Save();
        }

        public void IgnorePlayer(int playerId)
        {
            if (HasIgnoredPlayer(playerId))
            {
                return;
            }

            ignoredPlayers.Add(playerId);
        }

        public bool HasIgnoredPlayer(int playerId)
        {
            return ignoredPlayers.Contains(playerId);
        }

        public void RespawnLocalPlayer(PlayerRespawnArgs args)
        {
            onLocalPlayerRespawning.OnNext(args);
        }

        public void SetActiveLocomotions(bool isActive)
        {
            onLocomotionsActivationUpdating.OnNext(isActive);
        }

        public void TeleportLocalPlayer(PlayerTeleportArgs args)
        {
            onLocalPlayerTeleporting.OnNext(args);
        }

        public void SetLocalPlayerAge(bool isParent)
        {
            var scale = CoreConstants.WorldScale * (isParent ? 1 : 0.75f);
            SetLocalPlayerScale(scale);
        }

        public void SetLocalPlayerScale(float scale)
        {
            if (localPlayer.Value != null)
            {
                localPlayer.Value.SetScale(scale);
            }
        }
    }
}