using Game.Core;
using UnityEngine;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        private readonly IAsyncReactiveProperty<float> scale = new AsyncReactiveProperty<float>(CoreConstants.WorldScale);
        public IReadOnlyAsyncReactiveProperty<float> Scale => scale;

        public override void SetScale(float newScale)
        {
            if (HasStateAuthority)
            {
                // Set the transform scale directly - NetworkTransform will sync it automatically
                transform.localScale = newScale * Vector3.one;

                // Update our reactive property for local subscriptions
                scale.Value = newScale;
            }
        }

        public float GetCurrentScale()
        {
            return transform.localScale.x;
        }
    }
}
