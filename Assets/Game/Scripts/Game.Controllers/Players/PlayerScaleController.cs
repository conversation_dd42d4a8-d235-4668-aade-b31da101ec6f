using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Views.Players;
using Modules.Core;
using Modules.XR;
using VContainer;

namespace Game.Controllers.Players
{
    public class PlayerScaleController : ControllerBase
    {
        private IXRPlayer xrPlayer;
        private PlayersModel playersModel;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;
        private OfflinePlayerActor OfflinePlayer => playersModel.OfflineLocalPlayer.Value;

        [Inject]
        private void Construct(PlayersModel playersModel, IXRPlayer xrPlayer)
        {
            this.xrPlayer = xrPlayer;
            this.playersModel = playersModel;

            playersModel.OnLocalPlayerBeforeCreated.Subscribe(HandleLocalPlayerBeforeSpawned).AddTo(DisposeCancellationToken);
            playersModel.OfflineLocalPlayer.Where(p => p).Subscribe(HandleOfflinePlayer).AddTo(DisposeCancellationToken);
            playersModel.LocalPlayerScale.Subscribe(HandleScale).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayerBeforeSpawned(PlayerActor player)
        {
            player.SetScale(playersModel.LocalPlayerScale.Value);
            player.SetNetworkedScale(playersModel.LocalPlayerScale.Value);
        }

        private void HandleOfflinePlayer(OfflinePlayerActor player)
        {
            player.SetScale(playersModel.LocalPlayerScale.Value);
        }

        private void HandleScale(float scale)
        {
            xrPlayer.SetScale(scale);

            if (LocalPlayer != null)
            {
                LocalPlayer.SetNetworkedScale(scale);
            }

            if (OfflinePlayer != null)
            {
                OfflinePlayer.SetScale(scale);
            }
        }
    }
}