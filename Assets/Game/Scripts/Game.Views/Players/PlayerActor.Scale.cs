using Fusion;
using UnityEngine;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        [Networked] [OnChangedRender(nameof(ChangeScaleNetworked))]
        private float ScaleNetworked { get; set; } = 1.0f;

        public void SetNetworkedScale(float scale)
        {
            if (HasStateAuthority)
            {
                ScaleNetworked = scale;
            }
        }

        private void ChangeScaleNetworked()
        {
            // Apply the networked scale to the transform
            transform.localScale = ScaleNetworked * Vector3.one;
            
            // Also update the capsule collider to match the scale
            if (capsuleCollider != null)
            {
                // The collider should scale with the player
                var originalHeight = capsuleCollider.height / transform.localScale.y;
                var originalRadius = capsuleCollider.radius / transform.localScale.x;
                capsuleCollider.height = originalHeight * ScaleNetworked;
                capsuleCollider.radius = originalRadius * ScaleNetworked;
            }
        }

        public float GetNetworkedScale()
        {
            return ScaleNetworked;
        }
    }
}
