using Fusion;
using UnityEngine;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        [Networked] [OnChangedRender(nameof(ChangeScaleNetworked))]
        private float ScaleNetworked { get; set; } = 1.0f;

        public void SetNetworkedScale(float scale)
        {
            if (HasStateAuthority)
            {
                ScaleNetworked = scale;
            }
        }

        private void ChangeScaleNetworked()
        {
            // For remote players, just apply the visual scale
            if (!HasStateAuthority)
            {
                transform.localScale = ScaleNetworked * Vector3.one;
            }
        }

        public float GetNetworkedScale()
        {
            return ScaleNetworked;
        }
    }
}
